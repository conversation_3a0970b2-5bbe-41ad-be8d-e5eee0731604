'use client';

import { useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { NotificationPermissionBanner } from '@/components/ui/notification-permission-banner';
import { useStagedProducts } from '@/lib/hooks/use-staged-products-query';
import { useTikTokShops } from '@/lib/hooks/use-tiktok-shops-query';
import { Package, Upload, Store, AlertCircle, RefreshCw, Search, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { toast } from 'sonner';

export default function ClientDashboardPage() {
  return <DashboardContent />;
}

function DashboardContent() {
  const queryClient = useQueryClient();

  // Get staged products data
  const { data: stagedProductsData, isLoading: isLoadingStagedProducts } = useStagedProducts({ limit: 1000 });
  const stagedProducts = stagedProductsData?.data || [];

  // Get TikTok shops data
  const { data: shopsData, isLoading: isLoadingShops } = useTikTokShops({ limit: 100 });
  const shops = shopsData?.data || [];

  // Calculate stats
  const stats = useMemo(() => {
    if (!stagedProducts || stagedProducts.length === 0) {
      return {
        totalStagedProducts: 0,
        readyToUpload: 0,
        uploaded: 0,
        failed: 0,
      };
    }

    const readyToUpload = stagedProducts.filter(p => !p.uploadId).length;
    const uploaded = stagedProducts.filter(p => p.uploadId && p.upload?.status === 'COMPLETED').length;
    const failed = stagedProducts.filter(p => p.uploadId && p.upload?.status === 'FAILED').length;

    return {
      totalStagedProducts: stagedProducts.length,
      readyToUpload,
      uploaded,
      failed,
    };
  }, [stagedProducts]);

  const handleRefresh = () => {
    // This will trigger a refetch of all queries
    queryClient.invalidateQueries();
    toast.success('Data refreshed');
  };

  return (
    <div className="space-y-6">
      {/* Notification Permission Banner */}
      <NotificationPermissionBanner />

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="icon" onClick={handleRefresh}>
            <RefreshCw className={`h-4 w-4 ${isLoadingStagedProducts || isLoadingShops ? 'animate-spin' : ''}`} />
          </Button>
          <Button asChild>
            <Link href="/client/search">Search Products</Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staged Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoadingStagedProducts ? '...' : stats.totalStagedProducts}</div>
            <p className="text-xs text-muted-foreground">
              Products ready for processing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready to Upload</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoadingStagedProducts ? '...' : stats.readyToUpload}</div>
            <p className="text-xs text-muted-foreground">
              Products not yet uploaded
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successfully Uploaded</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoadingStagedProducts ? '...' : stats.uploaded}</div>
            <p className="text-xs text-muted-foreground">
              Products uploaded to TikTok Shop
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Uploads</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoadingStagedProducts ? '...' : stats.failed}</div>
            <p className="text-xs text-muted-foreground">
              Products with upload errors
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Recent Staged Products</CardTitle>
            <CardDescription>
              Your most recently added staged products
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingStagedProducts ? (
              <p>Loading...</p>
            ) : stagedProducts && stagedProducts.length > 0 ? (
              <div className="space-y-2">
                {stagedProducts.slice(0, 5).map((product) => (
                  <div key={product.id} className="flex items-center justify-between rounded-lg border p-3">
                    <div className="space-y-1">
                      <h3 className="text-sm font-medium">{product.title}</h3>
                      <p className="text-xs text-muted-foreground">
                        {product.upload?.status || 'Not uploaded'}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/client/tiktok/upload`}>
                          Upload
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p>No staged products found. <Link href="/client/search" className="text-blue-600 hover:underline">Search for products</Link> to get started.</p>
            )}
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and actions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start" asChild>
              <Link href="/client/search">
                <Search className="mr-2 h-4 w-4" />
                Search Products
              </Link>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <Link href="/client/tiktok/upload">
                <Upload className="mr-2 h-4 w-4" />
                Upload to TikTok Shop
              </Link>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <Link href="/client/template">
                <FileText className="mr-2 h-4 w-4" />
                Manage Templates
              </Link>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <Link href="/client/tiktok/store">
                <Store className="mr-2 h-4 w-4" />
                TikTok Shops
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Connected TikTok Shops</CardTitle>
            <CardDescription>
              Your connected TikTok shops
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingShops ? (
              <p>Loading shops...</p>
            ) : shops && shops.length > 0 ? (
              <div className="space-y-2">
                {shops.map((shop) => (
                  <div key={shop.id} className="flex items-center justify-between rounded-lg border p-3">
                    <div className="space-y-1">
                      <p className="font-medium leading-none">{shop.name}</p>
                      <p className="text-sm text-muted-foreground">Region: {shop.region}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p>No shops connected. <Link href="/client/tiktok/store" className="text-blue-600 hover:underline">Connect a shop</Link> to get started.</p>
            )}
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current system status and notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Notification System</span>
                <span className="text-xs text-green-600">Active</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Job Monitoring</span>
                <span className="text-xs text-green-600">Running</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Auto Crawler</span>
                <span className="text-xs text-blue-600">Available</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
