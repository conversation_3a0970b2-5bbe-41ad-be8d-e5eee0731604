import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { ProductUploadStatus } from '../entities/product-upload.entity';

export class CreateProductUploadDto {
  @ApiProperty({
    description: 'Staged product ID',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  stagedProductId: number;

  @ApiProperty({
    description: 'TikTok shop ID',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  tiktokShopId: number;
}

export class ProductUploadResponseDto {
  @ApiProperty({
    description: 'Product upload ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Staged product ID',
    example: 1,
  })
  stagedProductId: number;

  @ApiProperty({
    description: 'TikTok shop ID',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({
    description: 'Upload status',
    enum: ProductUploadStatus,
    example: ProductUploadStatus.PENDING,
  })
  status: ProductUploadStatus;

  @ApiProperty({
    description: 'TikTok product ID (if successful)',
    example: '7082427311584347905',
    required: false,
  })
  tiktokProductId?: string;

  @ApiProperty({
    description: 'Error message (if failed)',
    example: 'Failed to upload product: Invalid category ID',
    required: false,
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'Background job ID',
    example: '123',
    required: false,
  })
  jobId?: string;

  @ApiProperty({
    description: 'Upload progress (0-100)',
    example: 50,
  })
  progress: number;

  @ApiProperty({
    description: 'Created date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Completed date',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  completedAt?: Date;

  @ApiProperty({
    description: 'TikTok shop friendly name',
    example: 'My Main Beauty Store',
    required: false,
  })
  shopFriendlyName?: string;

  @ApiProperty({
    description: 'TikTok shop code',
    example: 'CNGBCBA4LLU8',
    required: false,
  })
  shopCode?: string;
}

export class ProductUploadStatusResponseDto {
  @ApiProperty({
    description: 'Product upload ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Upload status',
    enum: ProductUploadStatus,
    example: ProductUploadStatus.IN_PROGRESS,
  })
  status: ProductUploadStatus;

  @ApiProperty({
    description: 'Upload progress (0-100)',
    example: 50,
  })
  progress: number;

  @ApiProperty({
    description: 'TikTok product ID (if successful)',
    example: '7082427311584347905',
    required: false,
  })
  tiktokProductId?: string;

  @ApiProperty({
    description: 'Error message (if failed)',
    example: 'Failed to upload product: Invalid category ID',
    required: false,
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'Background job ID',
    example: '123',
    required: false,
  })
  jobId?: string;

  @ApiProperty({
    description: 'Created date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Completed date',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  completedAt?: Date;
}
